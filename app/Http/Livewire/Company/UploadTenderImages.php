<?php


namespace App\Http\Livewire\Company;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\TenderResult;
use App\Models\TenderResultImage;
use Carbon\Carbon;

class UploadTenderImages extends Component
{
    use WithFileUploads;

    public $show = false;
    public $tenderId;
    public $tenderResultId;
    public $images = [];
    public $imageMetadata = [];

    protected $listeners = ['openTenderImageUploadModal', 'receiveImageMetadata'];

    public function openTenderImageUploadModal($tenderId)
    {
        logger()->info('openModal called with tenderId: ' . $tenderId);
        
        try {
            $this->tenderId = $tenderId;
            
            $tenderResult = TenderResult::where('tender_id', $tenderId)->first();
            
            if ($tenderResult) {
                logger()->info('Found tender result: ', [
                    'id' => $tenderResult->id, 
                    'id_type' => gettype($tenderResult->id),
                    'tender_id' => $tenderResult->tender_id,
                    'created_at' => $tenderResult->created_at,
                    'object_type' => get_class($tenderResult)
                ]);
                
                $this->tenderResultId = $tenderResult->id;
                $this->show = true;
            } else {
                // Handle the case where no tender result exists
                logger()->warning('No tender result found for tender ID: ' . $tenderId);
                $this->show = false;
                session()->flash('error', 'Images cannot be uploaded yet. The tender result has not been finalized by an administrator.');
            }
        } catch (\Exception $e) {
            logger()->error('Error in openTenderImageUploadModal: ' . $e->getMessage());
            logger()->error($e->getTraceAsString());
            $this->show = false;
            session()->flash('error', 'An error occurred while opening the upload modal: ' . $e->getMessage());
        }
    }

    public function receiveImageMetadata($metadata)
    {
        $this->imageMetadata = $metadata;
        logger()->info('Received image metadata:', $this->imageMetadata);
    }

    public function uploadImages()
    {
        try {
            // Since we only show the upload UI when a tender result exists, 
            // this is just a safety check
            if (!$this->tenderResultId) {
                logger()->error('TenderResultId is missing');
                session()->flash('error', 'Unable to upload images. The tender result has not been finalized by an administrator.');
                $this->show = false;
                return;
            }
            
            $this->validate([
                'images.*' => 'image|max:8192', // 8MB Max - standardized with other uploads
            ]);
            
            // Log the tender result ID we're trying to find
            logger()->info('Looking for tender result with ID: ' . $this->tenderResultId . ' (type: ' . gettype($this->tenderResultId) . ')');
            
            // Try to get the tender result record with exact ID match
            $tenderResult = TenderResult::find($this->tenderResultId);
            
            // If not found directly, try with where clause
            if (!$tenderResult) {
                logger()->warning('TenderResult not found with find(), trying with where()');
                $tenderResult = TenderResult::where('id', $this->tenderResultId)->first();
            }
            
            // If still not found, try by tender_id
            if (!$tenderResult) {
                logger()->warning('TenderResult still not found, trying by tender_id: ' . $this->tenderId);
                $tenderResult = TenderResult::where('tender_id', $this->tenderId)->first();
                
                if ($tenderResult) {
                    logger()->info('Found by tender_id instead');
                    $this->tenderResultId = $tenderResult->id;
                }
            }
            
            if (!$tenderResult) {
                logger()->error('TenderResult with ID ' . $this->tenderResultId . ' not found');
                session()->flash('error', 'Unable to upload images. The tender result could not be found.');
                $this->show = false;
                return;
            }
            
            logger()->info('Found tender result: ', [
                'id' => $tenderResult->id, 
                'id_type' => gettype($tenderResult->id),
                'tender_id' => $tenderResult->tender_id,
                'created_at' => $tenderResult->created_at
            ]);
            
            foreach ($this->images as $index => $image) {
                try {
                    // Store the image in S3 progress-images disk
                    $path = $image->store('/', 'progress-images');
                    logger()->info('Stored image at S3 path: ' . $path);
                    
                    // Get metadata for this image if available
                    $metadata = $this->imageMetadata[$index] ?? [];
                    
                    // Debug the tender result ID value
                    logger()->info('Tender result ID details for image creation:', [
                        'id' => $tenderResult->id,
                        'id_type' => gettype($tenderResult->id),
                        'id_length' => strlen($tenderResult->id)
                    ]);
                    
                    $imageData = [
                        'tender_result_id' => (string)$tenderResult->id, // Ensure it's a string type
                        'title' => $image->getClientOriginalName(),
                        'path' => $path,
                        'size' => $image->getSize(),
                        'extension' => $image->getClientOriginalExtension(),
                        'meta' => json_encode([]), // Ensure this is stored as a JSON string
                    ];
                    
                    // Add GPS data if available
                    if (!empty($metadata)) {
                        if (isset($metadata['latitude']) && isset($metadata['longitude'])) {
                            $imageData['latitude'] = $metadata['latitude'];
                            $imageData['longitude'] = $metadata['longitude'];
                            $imageData['gps_timestamp'] = isset($metadata['timestamp']) 
                                ? Carbon::parse($metadata['timestamp']) 
                                : now();
                        }
                        
                        if (isset($metadata['location'])) {
                            $imageData['location_name'] = $metadata['location'];
                        }
                    }
                    
                    // Try to create the image record directly
                    logger()->info('Creating tender result image with data: ', ['tender_result_id' => $imageData['tender_result_id']]);
                    
                    // More detailed logging for debugging
                    logger()->info('Full image data for upload: ', $imageData);
                    
                    try {
                        // Try to create the image through the relationship instead
                        $createdImage = $tenderResult->images()->create([
                            'title' => $image->getClientOriginalName(),
                            'path' => $path,
                            'size' => $image->getSize(),
                            'extension' => $image->getClientOriginalExtension(),
                            'meta' => json_encode([]),
                            'latitude' => $imageData['latitude'] ?? null,
                            'longitude' => $imageData['longitude'] ?? null,
                            'location_name' => $imageData['location_name'] ?? null,
                            'gps_timestamp' => $imageData['gps_timestamp'] ?? null,
                        ]);
                        
                        logger()->info('Image created successfully with ID: ' . $createdImage->id);
                    } catch (\Exception $createException) {
                        logger()->error('Failed to create image record: ' . $createException->getMessage());
                        throw $createException;
                    }
                    
                } catch (\Exception $e) {
                    logger()->error('Error uploading image: ' . $e->getMessage());
                    logger()->error($e->getTraceAsString());
                }
            }
        
            $this->show = false;
            $this->reset(['images', 'imageMetadata']);
            logger()->info('All images processed, hiding modal and showing success message');
            session()->flash('message', 'Images uploaded successfully.');
            
            // Dispatch a browser event for debugging purposes
            $this->dispatchBrowserEvent('images-uploaded-successfully');
            
        } catch (\Exception $e) {
            logger()->error('Error in uploadImages: ' . $e->getMessage());
            logger()->error($e->getTraceAsString());
            session()->flash('error', 'An error occurred while uploading images: ' . $e->getMessage());
            $this->show = false;
        }
    }

    public function render()
    {
        return view('livewire.company.upload-tender-images');
    }
}